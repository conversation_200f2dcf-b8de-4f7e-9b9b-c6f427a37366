jQuery(document).ready(function($) {
    // Tab switching functionality
    $(".simple-nav a").click(function(e) {
        e.preventDefault();
        $(".simple-nav a").removeClass("active");
        $(this).addClass("active");
        
        var target = $(this).attr("href");
        $(".tab-content").hide();
        $(target).show();
    });
    
    // Active plugins filter functionality
    $("#active-only-filter").change(function() {
        var showActiveOnly = $(this).is(":checked");
        
        if (showActiveOnly) {
            $(".plugin-row.inactive").hide();
            $(".plugin-row.active").show();
        } else {
            $(".plugin-row").show();
        }
    });
    
    // Copy to clipboard functionality
    $(".copy-button").click(function() {
        var button = $(this);
        var tabContent = button.closest(".tab-content");
        var table = tabContent.find("table");
        var visibleRows = table.find("tr.plugin-row:visible");
        var textContent = "";
        
        visibleRows.each(function() {
            var cells = $(this).find("td");
            var rowText = "";
            cells.each(function(index) {
                if (index > 0) rowText += "\t";
                rowText += $(this).text().trim();
            });
            textContent += rowText + "\n";
        });
        
        // Copy to clipboard with modern API and fallback
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(textContent).then(function() {
                showCopySuccess(button);
            }).catch(function() {
                fallbackCopy(textContent, button);
            });
        } else {
            fallbackCopy(textContent, button);
        }
    });
    
    // Fallback copy method for older browsers
    function fallbackCopy(text, button) {
        var textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.opacity = "0";
        document.body.appendChild(textArea);
        textArea.select();
        
        try {
            document.execCommand("copy");
            showCopySuccess(button);
        } catch (err) {
            console.error("Copy failed:", err);
        }
        
        document.body.removeChild(textArea);
    }
    
    // Show copy success message
    function showCopySuccess(button) {
        var successMsg = button.next(".copy-success");
        if (successMsg.length === 0) {
            successMsg = $('<span class="copy-success">' + pluginReportL10n.copied + '</span>');
            button.after(successMsg);
        }
        successMsg.show();
        setTimeout(function() {
            successMsg.fadeOut();
        }, 2000);
    }
});
