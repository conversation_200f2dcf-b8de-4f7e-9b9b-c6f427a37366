=== Simple Plugin Report ===
Contributors: janpencik
Tags: plugins, admin, report, list, management
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Displays a clean, organized list of all installed plugins with their versions and status. Simple, fast, and no configuration needed.

== Description ==

Simple Plugin Report adds a straightforward plugin management page to your WordPress admin. It provides a clean, organized view of all your installed plugins with their versions and activation status.

**What it does:**
* Shows all installed plugins in an easy-to-read format
* Displays plugin names, versions, and active/inactive status
* Offers two viewing modes: compact text view and detailed table view
* Allows filtering to show only active plugins
* Provides one-click copying of the plugin list to clipboard
* Works immediately after activation - no setup required

**Perfect for:**
* Site administrators who need quick plugin overviews
* Developers managing multiple WordPress installations
* Support teams helping clients with plugin-related issues
* Anyone who wants a simple way to export their plugin list

**Key Features:**
* **Two View Modes**: Switch between compact text view and detailed table view
* **Smart Filtering**: Toggle to show only active plugins
* **Copy to Clipboard**: Export your plugin list with one click
* **Clean Interface**: Integrates seamlessly with WordPress admin design
* **Translation Ready**: Fully internationalized and ready for translation
* **Lightweight**: Minimal code, fast loading, no database queries
* **Zero Configuration**: Works immediately after activation

The plugin adds a "Plugin Report" page under the Plugins menu in your WordPress admin. No settings to configure, no complex features to learn - just activate and use.

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/simple-plugin-report` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress.
3. Navigate to Plugins > Plugin Report to view your plugin list.

== Frequently Asked Questions ==

= Where do I find the plugin report? =

After activation, go to Plugins > Plugin Report in your WordPress admin menu.

= Can I export the plugin list? =

Yes! Use the "Copy to clipboard" button to copy the plugin list. You can then paste it into any document, spreadsheet, or text editor.

= Does this plugin store any data? =

No, the plugin doesn't store any data in your database. It reads plugin information directly from WordPress when you view the report page.

= Can I filter the plugins shown? =

Yes, you can use the "Active plugins only" checkbox to show only currently active plugins.

= Is the plugin translatable? =

Yes, the plugin is fully translation-ready with proper internationalization support.

= Does this work with multisite? =

The plugin works on multisite installations and shows plugins for the current site.

== Screenshots ==

1. Text view showing all plugins with versions and status
2. Table view with organized columns for name, status, and version
3. Filter option to show only active plugins
4. Copy to clipboard functionality

== Changelog ==

= 1.0.0 =
* Initial release
* Text and table view modes
* Active/inactive plugin filtering
* Copy to clipboard functionality
* Translation ready
* Clean, responsive design

== Upgrade Notice ==

= 1.0.0 =
Initial release of Simple Plugin Report.
