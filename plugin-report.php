<?php
/**
 * Plugin Name: Plugin Report
 * Description: Simple plugin that adds a subpage to Plugins admin menu showing plugin information
 * Version: 1.0.0
 * Author: Your Name
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PluginReport {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    public function add_admin_menu() {
        add_plugins_page(
            'Plugin Report',
            'Plugin Report', 
            'manage_options',
            'plugin-report',
            array($this, 'admin_page')
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== 'plugins_page_plugin-report') {
            return;
        }
        
        // Add inline JavaScript for tab switching
        wp_add_inline_script('jquery', '
            jQuery(document).ready(function($) {
                $(".nav-tab").click(function(e) {
                    e.preventDefault();
                    $(".nav-tab").removeClass("nav-tab-active");
                    $(this).addClass("nav-tab-active");
                    
                    var target = $(this).attr("href");
                    $(".tab-content").hide();
                    $(target).show();
                });
            });
        ');
    }
    
    public function admin_page() {
        $plugins = get_plugins();
        ?>
        <style>
            .plugin-report .wp-list-table {
                width: 100%;
            }
            .plugin-report .card {
                padding: 0;
                width: 600px;
                max-width: 100%;
            }
            .plugin-report .wp-list-table th {
                text-align: left;
            }
            .plugin-report .wp-list-table th:last-child {
                width: 10px;
            }
            .plugin-report .wp-list-table th, 
            .plugin-report .wp-list-table td {
                padding: 3px 5px;
            }
        </style>

        <div class="wrap plugin-report">
            <h1>Plugin Report</h1>
            
            <nav class="nav-tab-wrapper">
                <a href="#text-view" class="nav-tab nav-tab-active">Text View</a>
                <a href="#table-view" class="nav-tab">Table View</a>
            </nav>
            
            <div id="text-view" class="tab-content">
                <div class="card">
                    <table class="wp-list-table fixed striped">
                        <tbody>
                            <?php foreach ($plugins as $plugin_file => $plugin_data): ?>
                            <tr>
                                <td><?php echo esc_html($plugin_data['Name'] . ' - ' . $plugin_data['Version']); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div id="table-view" class="tab-content" style="display: none;">
                <div class="card">
                    <table class="wp-list-table fixed striped">
                        <thead>
                            <tr>
                                <th scope="col">Plugin Name</th>
                                <th scope="col">Version</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($plugins as $plugin_file => $plugin_data): ?>
                            <tr>
                                <td><?php echo esc_html($plugin_data['Name']); ?></td>
                                <td><?php echo esc_html($plugin_data['Version']); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php
    }
}

// Initialize the plugin
new PluginReport();
