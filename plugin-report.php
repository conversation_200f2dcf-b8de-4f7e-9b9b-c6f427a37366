<?php
/**
 * Plugin Name: Plugin Report
 * Description: Simple plugin that adds a subpage to Plugins admin menu showing plugin information
 * Version: 1.0.0
 * Author: Bohemia Plugins
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PluginReport {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    public function add_admin_menu() {
        add_plugins_page(
            'Plugin Report',
            'Plugin Report', 
            'manage_options',
            'plugin-report',
            array($this, 'admin_page')
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== 'plugins_page_plugin-report') {
            return;
        }
        
        // Add inline JavaScript for tab switching, filtering, and copying
        wp_add_inline_script('jquery', '
            jQuery(document).ready(function($) {
                $(".simple-nav a").click(function(e) {
                    e.preventDefault();
                    $(".simple-nav a").removeClass("active");
                    $(this).addClass("active");

                    var target = $(this).attr("href");
                    $(".tab-content").hide();
                    $(target).show();
                });

                $("#active-only-filter").change(function() {
                    var showActiveOnly = $(this).is(":checked");

                    if (showActiveOnly) {
                        $(".plugin-row.inactive").hide();
                        $(".plugin-row.active").show();
                    } else {
                        $(".plugin-row").show();
                    }
                });

                $(".copy-button").click(function() {
                    var button = $(this);
                    var tabContent = button.closest(".tab-content");
                    var table = tabContent.find("table");
                    var visibleRows = table.find("tr.plugin-row:visible");
                    var textContent = "";

                    visibleRows.each(function() {
                        var cells = $(this).find("td");
                        var rowText = "";
                        cells.each(function(index) {
                            if (index > 0) rowText += "\\t";
                            rowText += $(this).text().trim();
                        });
                        textContent += rowText + "\\n";
                    });

                    // Copy to clipboard
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(textContent).then(function() {
                            showCopySuccess(button);
                        });
                    } else {
                        // Fallback for older browsers
                        var textArea = document.createElement("textarea");
                        textArea.value = textContent;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand("copy");
                        document.body.removeChild(textArea);
                        showCopySuccess(button);
                    }
                });

                function showCopySuccess(button) {
                    var successMsg = button.next(".copy-success");
                    if (successMsg.length === 0) {
                        successMsg = $("<span class=\"copy-success\">Copied!</span>");
                        button.after(successMsg);
                    }
                    successMsg.show();
                    setTimeout(function() {
                        successMsg.fadeOut();
                    }, 2000);
                }
            });
        ');
    }
    
    public function admin_page() {
        $plugins = get_plugins();

        // Include the plugin.php file to access is_plugin_active function
        if (!function_exists('is_plugin_active')) {
            include_once(ABSPATH . 'wp-admin/includes/plugin.php');
        }
        ?>
        <style>
            .plugin-report .wp-list-table {
                min-width: 500px;
                width: 100%;
                max-width: 600px;
                margin-top: 10px;
                border-spacing: 0;
                table-layout: auto;
            }
            .plugin-report .wp-list-table td:nth-child(2) {
                width: 20%;
                white-space: nowrap;
            }
            .plugin-report .wp-list-table td {
                font-size: 13px;
                line-height: 1.6;
                vertical-align: top;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .plugin-report .wp-list-table td:first-child {
                white-space: normal;
                min-width: 300px;
            }
            .plugin-report .simple-nav {
                margin-top: 10px;
                margin-left: -.2em;
                float: none;
                text-align: left;
                margin-bottom: 10px;
            }
            .plugin-report .simple-nav a {
                text-decoration: none;
                color: #0073aa;
                font-weight: normal;
            }
            .plugin-report .simple-nav a:focus,
            .plugin-report .simple-nav a:active {
                outline: none;
                box-shadow: none;
            }
            .plugin-report .simple-nav a.active {
                color: #000;
                font-weight: 600;
            }
            .plugin-report .filter-controls {
                margin-top: 10px;
                background: #f1f1f1;
                border-radius: 3px;
            }
            .plugin-report .filter-controls label {
                margin-right: 15px;
            }
            .plugin-report .copy-button-wrapper {
                margin-top: 15px;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .plugin-report .copy-success {
                color: #007017;
                font-size: 13px;
            }
        </style>

        <div class="wrap plugin-report">
            <h1>Plugin Report</h1>

            <div class="filter-controls">
                <label for="active-only-filter">
                    <input type="checkbox" id="active-only-filter" />
                    Active plugins only
                </label>
            </div>

            <ul class="subsubsub simple-nav">
                <li class="all">
                    <a href="#text-view" class="active">Text View</a> |
                </li>
                <li class="all">
                    <a href="#table-view">Table View</a>
                </li>
            </ul>
            
            <div id="text-view" class="tab-content">
                <table class="wp-list-table fixed">
                    <tbody>
                        <?php foreach ($plugins as $plugin_file => $plugin_data): ?>
                        <?php
                            $is_active = is_plugin_active($plugin_file);
                            $row_class = $is_active ? 'plugin-row active' : 'plugin-row inactive';
                            $plugin_extra = $is_active ? '' : ' (inactive)';
                        ?>
                        <tr class="<?php echo esc_attr($row_class); ?>">
                            <td><?php
                                echo esc_html($plugin_data['Name'] . ' ' . $plugin_data['Version'] . $plugin_extra);
                            ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <div class="copy-button-wrapper wp-core-ui">
                    <button class="button copy-button">Copy to clipboard</button>
                </div>
            </div>

            <div id="table-view" class="tab-content" style="display: none;">
                <table class="wp-list-table fixed">
                    <tbody>
                        <?php foreach ($plugins as $plugin_file => $plugin_data): ?>
                        <?php
                            $is_active = is_plugin_active($plugin_file);
                            $row_class = $is_active ? 'plugin-row active' : 'plugin-row inactive';
                            $plugin_extra = $is_active ? '' : ' (inactive)';
                        ?>
                        <tr class="<?php echo esc_attr($row_class); ?>">
                            <td><?php
                                echo esc_html($plugin_data['Name'] . $plugin_extra);
                            ?></td>
                            <td><?php echo esc_html($plugin_data['Version']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <div class="copy-button-wrapper wp-core-ui">
                    <button class="button copy-button">Copy to clipboard</button>
                </div>
            </div>
        </div>
        <?php
    }
}

// Initialize the plugin
new PluginReport();
