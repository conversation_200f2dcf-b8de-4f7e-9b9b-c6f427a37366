<?php
/**
 * Plugin Name: Simple Plugin Report
 * Description: Displays a clean, organized list of all installed plugins with their versions and status. View plugins in text or table format, filter by active status, and copy the list to clipboard. No configuration needed - just activate and use.
 * Version: 1.0.0
 * Author: <PERSON>
 * Text Domain: simple-plugin-report
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PluginReport {

    public function __construct() {
        add_action('init', array($this, 'load_textdomain'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }

    public function load_textdomain() {
        load_plugin_textdomain('simple-plugin-report', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function add_admin_menu() {
        add_plugins_page(
            __('Plugin Report', 'simple-plugin-report'),
            __('Plugin Report', 'simple-plugin-report'),
            'manage_options',
            'plugin-report',
            array($this, 'admin_page')
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== 'plugins_page_plugin-report') {
            return;
        }

        // Enqueue the JavaScript file
        wp_enqueue_script(
            'plugin-report-js',
            plugin_dir_url(__FILE__) . 'assets/plugin-report.js',
            array('jquery'),
            '1.0.0',
            true
        );

        // Localize script for translations
        wp_localize_script('plugin-report-js', 'pluginReportL10n', array(
            'copied' => __('Copied!', 'simple-plugin-report')
        ));
    }
    
    public function admin_page() {
        $plugins = get_plugins();

        // Include the plugin.php file to access is_plugin_active function
        if (!function_exists('is_plugin_active')) {
            include_once(ABSPATH . 'wp-admin/includes/plugin.php');
        }
        ?>
        <style>
            .plugin-report .wp-list-table {
                min-width: 500px;
                width: 100%;
                max-width: 600px;
                margin-top: 10px;
                border-spacing: 0;
                table-layout: auto;
            }
            .plugin-report .wp-list-table td {
                text-align: left;
            }
            .plugin-report .wp-list-table td {
                font-size: 13px;
                line-height: 1.6;
                vertical-align: top;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100px;
            }
            .plugin-report .wp-list-table td:first-child {
                white-space: nowrap;
            }
            .plugin-report .simple-nav {
                margin-top: 10px;
                margin-left: -.2em;
                float: none;
                text-align: left;
                margin-bottom: 10px;
            }
            .plugin-report .simple-nav a {
                text-decoration: none;
                color: #0073aa;
                font-weight: normal;
            }
            .plugin-report .simple-nav a:focus,
            .plugin-report .simple-nav a:active {
                outline: none;
                box-shadow: none;
            }
            .plugin-report .simple-nav a.active {
                color: #000;
                font-weight: 600;
            }
            .plugin-report .filter-controls {
                margin-top: 10px;
                background: #f1f1f1;
                border-radius: 3px;
            }
            .plugin-report .filter-controls label {
                margin-right: 15px;
            }
            .plugin-report .copy-button-wrapper {
                margin-top: 15px;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .plugin-report .copy-success {
                color: #007017;
                font-size: 13px;
            }
            .plugin-report .plugin-row.active {
                color: #2c3338;
                font-weight: 600;
            }
            @media (max-width: 782px) {
                .plugin-report .wp-list-table td:first-child {
                    white-space: normal;
                }
            }
        </style>

        <div class="wrap plugin-report">
            <h1><?php echo esc_html(__('Simple Plugin Report', 'simple-plugin-report')); ?></h1>

            <div class="filter-controls">
                <label for="active-only-filter">
                    <input type="checkbox" id="active-only-filter" />
                    <?php esc_html_e('Active plugins only', 'simple-plugin-report'); ?>
                </label>
            </div>

            <ul class="subsubsub simple-nav">
                <li class="all">
                    <a href="#text-view" class="active"><?php esc_html_e('Text View', 'simple-plugin-report'); ?></a> |
                </li>
                <li class="all">
                    <a href="#table-view"><?php esc_html_e('Table View', 'simple-plugin-report'); ?></a>
                </li>
            </ul>
            
            <div id="text-view" class="tab-content">
                <table class="wp-list-table fixed">
                    <tbody>
                        <?php foreach ($plugins as $plugin_file => $plugin_data): ?>
                        <?php
                            $is_active = is_plugin_active($plugin_file);
                            $row_class = $is_active ? 'plugin-row active' : 'plugin-row inactive';
                            $plugin_extra = $is_active ? '' : ' ' . __('(inactive)', 'simple-plugin-report');
                        ?>
                        <tr class="<?php echo esc_attr($row_class); ?>">
                            <td><?php
                                echo esc_html($plugin_data['Name'] . ' ' . $plugin_data['Version'] . $plugin_extra);
                            ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <div class="copy-button-wrapper wp-core-ui">
                    <button class="button copy-button"><?php esc_html_e('Copy to clipboard', 'simple-plugin-report'); ?></button>
                </div>
            </div>

            <div id="table-view" class="tab-content" style="display: none;">
                <table class="wp-list-table fixed">
                    <tbody>
                        <?php foreach ($plugins as $plugin_file => $plugin_data): ?>
                        <?php
                            $is_active = is_plugin_active($plugin_file);
                            $row_class = $is_active ? 'plugin-row active' : 'plugin-row inactive';
                            $status_text = $is_active ? __('Active', 'simple-plugin-report') : __('Inactive', 'simple-plugin-report');
                        ?>
                        <tr class="<?php echo esc_attr($row_class); ?>">
                            <td><?php echo esc_html($plugin_data['Name']); ?></td>
                            <td><?php echo esc_html($status_text); ?></td>
                            <td><?php echo esc_html($plugin_data['Version']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <div class="copy-button-wrapper wp-core-ui">
                    <button class="button copy-button">Copy to clipboard</button>
                </div>
            </div>
        </div>
        <?php
    }
}

// Initialize the plugin
new PluginReport();
